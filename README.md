# Quotex

A modern Elixir/Phoenix application with caching, background jobs, and real-time features.

## 🚀 Quick Start with Dev Containers

This project includes a complete VS Code development container configuration for a consistent development experience.

### Prerequisites

- [VS Code](https://code.visualstudio.com/)
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) for VS Code

### Getting Started

1. **Clone and Open**
   ```bash
   git clone <your-repo-url>
   cd quotex
   code .
   ```

2. **Open in Container**
   - VS Code will prompt you to "Reopen in Container" - click it
   - Or press `Ctrl+Shift+P` (Cmd+Shift+P on Mac) and select "Dev Containers: Reopen in Container"

3. **Wait for Setup**
   - The container will build automatically (first time takes a few minutes)
   - Setup script will install dependencies and configure the environment

4. **Create Phoenix App** (if this is a new project)
   ```bash
   mix phx.new . --app quotex --database postgres
   ```

5. **Start Development**
   ```bash
   mix phx.server
   # or use the alias: phx
   ```

Your Phoenix app will be available at http://localhost:4000

## 🛠️ Development Environment

The devcontainer includes:

### Services
- **Elixir 1.15** with OTP 26
- **Phoenix Framework** with LiveView
- **PostgreSQL 15** database
- **Redis 7** for caching and sessions
- **Node.js** for asset compilation

### Tools & Extensions
- ElixirLS (Language Server)
- Phoenix Framework support
- Git integration with GitLens
- Docker tools
- Testing and debugging support
- Code formatting and linting

### Available Ports
- `4000` - Phoenix server
- `4001` - LiveDashboard
- `5432` - PostgreSQL
- `6379` - Redis
- `8080` - pgAdmin (optional)
- `8081` - Redis Commander (optional)

## 📋 Development Commands

The setup includes helpful aliases:

```bash
# Phoenix
phx          # mix phx.server
iex          # iex -S mix

# Testing
test         # mix test
testw        # mix test.watch

# Code Quality
format       # mix format
credo        # mix credo
dialyzer     # mix dialyzer

# Database
db.setup     # mix ecto.setup
db.migrate   # mix ecto.migrate
db.rollback  # mix ecto.rollback
db.reset     # mix ecto.reset
```

## 🏗️ Architecture

Based on the project guidelines, Quotex uses:

- **Nebulex** for distributed caching
- **Redis** as the primary cache backend
- **Oban** for background job processing and scheduling
- **Redix** for direct Redis operations when needed
- **PromEx/Telemetry** for monitoring and observability
- **LiveDashboard** for real-time system insights

## 🧪 Testing

Run tests with:
```bash
mix test
# or
test
```

For continuous testing:
```bash
mix test.watch
# or
testw
```

## 📊 Monitoring

Access monitoring tools:
- **LiveDashboard**: http://localhost:4000/dashboard
- **pgAdmin**: http://localhost:8080 (<EMAIL> / admin)
- **Redis Commander**: http://localhost:8081

To enable optional GUI tools:
```bash
docker-compose --profile tools up -d
```

## 🔧 Configuration

Environment variables are configured in the devcontainer:
- `MIX_ENV=dev`
- `DATABASE_URL=************************************/quotex_dev`
- `REDIS_URL=redis://redis:6379/0`

## 📚 Project Structure

Following Phoenix conventions with additional organization:
```
lib/
├── quotex/              # Business logic
│   ├── cache/          # Nebulex cache modules
│   └── workers/        # Oban background workers
├── quotex_web/         # Web interface
└── quotex/application.ex
```

## 🤝 Contributing

1. Make your changes in the devcontainer
2. Run tests: `mix test`
3. Format code: `mix format`
4. Check with Credo: `mix credo`
5. Submit a pull request

## 📖 Documentation

- [Phoenix Framework](https://hexdocs.pm/phoenix/)
- [Elixir](https://hexdocs.pm/elixir/)
- [Nebulex](https://hexdocs.pm/nebulex/)
- [Oban](https://hexdocs.pm/oban/)

## 🐛 Troubleshooting

If you encounter issues:

1. **Container won't start**: Ensure Docker is running and try "Dev Containers: Rebuild Container"
2. **Database issues**: Check if PostgreSQL is running with `pg_isready -h db -p 5432 -U postgres`
3. **Redis issues**: Test with `redis-cli -h redis ping`
4. **Dependencies**: Clean and reinstall with `mix deps.clean --all && mix deps.get`

For more detailed troubleshooting, see [.devcontainer/README.md](.devcontainer/README.md).
