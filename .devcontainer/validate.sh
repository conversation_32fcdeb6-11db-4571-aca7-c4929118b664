#!/bin/bash

# Validation script for Quotex devcontainer configuration
# This script validates all configuration files before building the container

set -e

echo "🔍 Validating Quotex devcontainer configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Validate JSON syntax
echo "Validating devcontainer.json..."
if python3 -m json.tool .devcontainer/devcontainer.json > /dev/null 2>&1; then
    print_success "devcontainer.json has valid JSON syntax"
else
    print_error "devcontainer.json has JSON syntax errors"
    exit 1
fi

# Check required files exist
echo "Checking required files..."
required_files=(
    ".devcontainer/devcontainer.json"
    ".devcontainer/docker-compose.yml"
    ".devcontainer/Dockerfile"
    ".devcontainer/setup.sh"
    ".devcontainer/init-db.sql"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "$file exists"
    else
        print_error "$file is missing"
        exit 1
    fi
done

# Check setup script is executable
if [ -x ".devcontainer/setup.sh" ]; then
    print_success "setup.sh is executable"
else
    print_error "setup.sh is not executable"
    exit 1
fi

# Validate docker-compose syntax (if docker-compose is available)
if command -v docker-compose &> /dev/null; then
    echo "Validating docker-compose.yml..."
    if docker-compose -f .devcontainer/docker-compose.yml config > /dev/null 2>&1; then
        print_success "docker-compose.yml has valid syntax"
    else
        print_error "docker-compose.yml has syntax errors"
        exit 1
    fi
else
    print_info "docker-compose not available, skipping validation"
fi

# Check Dockerfile syntax (basic check)
echo "Checking Dockerfile..."
if grep -q "FROM elixir:" .devcontainer/Dockerfile; then
    print_success "Dockerfile has valid FROM instruction"
else
    print_error "Dockerfile missing or invalid FROM instruction"
    exit 1
fi

print_success "All validations passed! 🎉"
echo ""
print_info "Your devcontainer is ready to use. To get started:"
echo "  1. Open this project in VS Code"
echo "  2. Install the 'Dev Containers' extension if not already installed"
echo "  3. Press Ctrl+Shift+P (Cmd+Shift+P on Mac) and select 'Dev Containers: Reopen in Container'"
echo "  4. Wait for the container to build and setup to complete"
echo "  5. Start developing your Phoenix application!"
echo ""
