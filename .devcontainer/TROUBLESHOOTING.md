# Devcontainer Troubleshooting Guide

This guide helps you diagnose and fix common issues when opening the Quotex project in a VS Code development container.

## 🔍 Quick Diagnostics

Run the validation script to check for configuration issues:
```bash
./.devcontainer/validate.sh
```

## 🚨 Common Issues and Solutions

### 1. Container Fails to Build

**Symptoms:**
- "Failed to build container" error
- Docker build process stops with errors

**Solutions:**
1. **Check Docker is running:**
   ```bash
   docker --version
   docker ps
   ```

2. **Clean Docker cache:**
   ```bash
   docker system prune -a
   ```

3. **Rebuild without cache:**
   - In VS Code: `Ctrl+Shift+P` → "Dev Containers: Rebuild Container Without Cache"

4. **Check Dockerfile syntax:**
   ```bash
   docker build -f .devcontainer/Dockerfile --dry-run .
   ```

### 2. Container Starts but VS Code Can't Connect

**Symptoms:**
- Container appears to be running but VS Code shows connection errors
- "Failed to connect to the remote extension host server" error

**Solutions:**
1. **Check container is running:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml ps
   ```

2. **Restart the container:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml restart app
   ```

3. **Check container logs:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml logs app
   ```

### 3. Workspace Folder Issues

**Symptoms:**
- Files not visible in VS Code
- "Workspace folder not found" error
- Permission denied errors

**Solutions:**
1. **Verify workspace folder configuration:**
   - Check `workspaceFolder` in `devcontainer.json`
   - Ensure it matches the mount point in `docker-compose.yml`

2. **Fix permissions:**
   ```bash
   # Inside the container
   sudo chown -R vscode:vscode /workspaces
   ```

### 4. Database Connection Issues

**Symptoms:**
- Phoenix can't connect to PostgreSQL
- "Connection refused" errors for database

**Solutions:**
1. **Check PostgreSQL is running:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml ps db
   ```

2. **Test database connection:**
   ```bash
   pg_isready -h db -p 5432 -U postgres
   ```

3. **Check database logs:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml logs db
   ```

4. **Restart database service:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml restart db
   ```

### 5. Redis Connection Issues

**Symptoms:**
- Redis connection errors
- Cache operations failing

**Solutions:**
1. **Check Redis is running:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml ps redis
   ```

2. **Test Redis connection:**
   ```bash
   redis-cli -h redis ping
   ```

3. **Check Redis logs:**
   ```bash
   docker-compose -f .devcontainer/docker-compose.yml logs redis
   ```

### 6. Port Forwarding Issues

**Symptoms:**
- Can't access Phoenix server at localhost:4000
- Ports not forwarded correctly

**Solutions:**
1. **Check port forwarding in VS Code:**
   - Go to "Ports" tab in VS Code terminal panel
   - Ensure ports 4000, 4001, 5432, 6379 are forwarded

2. **Manually forward ports:**
   - In VS Code: `Ctrl+Shift+P` → "Ports: Focus on Ports View"
   - Click "Forward a Port" and add the required ports

3. **Check if services are listening:**
   ```bash
   # Inside container
   netstat -tlnp | grep -E ':(4000|4001|5432|6379)'
   ```

### 7. Extension Issues

**Symptoms:**
- ElixirLS not working
- Extensions not loading properly

**Solutions:**
1. **Reload window:**
   - `Ctrl+Shift+P` → "Developer: Reload Window"

2. **Reinstall extensions:**
   - `Ctrl+Shift+P` → "Dev Containers: Rebuild Container"

3. **Check extension logs:**
   - Go to Output panel → Select "ElixirLS" from dropdown

### 8. Node.js/npm Version Issues

**Symptoms:**
- npm install fails with version compatibility errors
- "npm ERR! engine Unsupported engine" messages
- Build fails during container creation with Node.js errors

**Solutions:**
1. **Check Node.js and npm versions:**
   ```bash
   node --version
   npm --version
   ```

2. **The Dockerfile pins specific versions to avoid compatibility issues:**
   - Node.js: 20.15.1
   - npm: 10.9.1
   - These versions are compatible with each other

3. **If you need different versions, update the Dockerfile:**
   ```dockerfile
   nodejs=20.15.1-r0 \
   npm=10.9.1-r0 \
   ```

### 9. Mix Dependencies Issues

**Symptoms:**
- `mix deps.get` fails
- Compilation errors

**Solutions:**
1. **Clean and reinstall dependencies:**
   ```bash
   mix deps.clean --all
   mix deps.get
   mix deps.compile
   ```

2. **Check Hex and Rebar:**
   ```bash
   mix local.hex --force
   mix local.rebar --force
   ```

3. **Clear build cache:**
   ```bash
   mix clean
   ```

## 🔧 Advanced Troubleshooting

### Check Container Environment
```bash
# Inside container
env | grep -E '(MIX_ENV|DATABASE_URL|REDIS_URL)'
```

### Inspect Container Configuration
```bash
docker inspect quotex-app-1
```

### Check Volume Mounts
```bash
docker volume ls | grep quotex
docker volume inspect quotex-build-cache
```

### View All Container Logs
```bash
docker-compose -f .devcontainer/docker-compose.yml logs --follow
```

## 📞 Getting Help

If you're still experiencing issues:

1. **Check VS Code Dev Containers logs:**
   - `Ctrl+Shift+P` → "Dev Containers: Show Container Log"

2. **Create a minimal reproduction:**
   - Try with a fresh clone of the repository
   - Test with a simple Phoenix project

3. **System information to include when reporting issues:**
   ```bash
   # Run these commands and include output
   docker --version
   docker-compose --version
   code --version
   uname -a
   ```

## 🔄 Reset Everything

If all else fails, completely reset the development environment:

```bash
# Stop and remove all containers
docker-compose -f .devcontainer/docker-compose.yml down -v

# Remove all related Docker images
docker rmi $(docker images | grep quotex | awk '{print $3}')

# Clean Docker system
docker system prune -a

# Rebuild container in VS Code
# Ctrl+Shift+P → "Dev Containers: Rebuild Container Without Cache"
```
