-- Initialize databases for Quotex development
-- This script runs when the PostgreSQL container starts for the first time

-- Create test database
CREATE DATABASE quotex_test;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE quotex_dev TO postgres;
GRANT ALL PRIVILEGES ON DATABASE quotex_test TO postgres;

-- Create extensions that might be needed
\c quotex_dev;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";

\c quotex_test;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";
