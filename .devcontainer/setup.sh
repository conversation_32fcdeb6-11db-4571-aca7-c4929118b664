#!/bin/bash

# Quotex Development Environment Setup Script
# This script runs after the devcontainer is created

set -e

echo "🚀 Setting up Quotex development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a Phoenix project
if [ ! -f "mix.exs" ]; then
    print_warning "No mix.exs found. This might be a new project."
    print_status "You can create a new Phoenix project with:"
    echo "  mix phx.new . --app quotex --database postgres"
    echo ""
fi

# Test Node.js setup
print_status "Testing Node.js setup..."
if [ -f ".devcontainer/test-nodejs.sh" ]; then
    .devcontainer/test-nodejs.sh
else
    print_info "Node.js test script not found, skipping..."
fi

# Install/update Hex and Rebar
print_status "Installing/updating Hex and Rebar..."
mix local.hex --force --if-missing
mix local.rebar --force --if-missing

# Install Phoenix if not already installed
print_status "Installing/updating Phoenix..."
mix archive.install hex phx_new --force

# Install dependencies if mix.exs exists
if [ -f "mix.exs" ]; then
    print_status "Installing Mix dependencies..."
    mix deps.get
    
    # Compile dependencies
    print_status "Compiling dependencies..."
    mix deps.compile
    
    # Install Node.js dependencies for assets if they exist
    if [ -f "assets/package.json" ]; then
        print_status "Installing Node.js dependencies for assets..."
        cd assets
        npm install
        cd ..
    fi
    
    # Setup database if ecto is available
    if mix help ecto.setup >/dev/null 2>&1; then
        print_status "Setting up database..."
        # Wait for database to be ready
        until pg_isready -h db -p 5432 -U postgres; do
            print_status "Waiting for PostgreSQL to be ready..."
            sleep 2
        done
        
        mix ecto.setup || print_warning "Database setup failed. You may need to run 'mix ecto.setup' manually."
    fi
else
    print_status "Creating a sample .env file for future use..."
    cat > .env.example << EOF
# Database Configuration
DATABASE_URL=************************************/quotex_dev
TEST_DATABASE_URL=************************************/quotex_test

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Phoenix Configuration
PHX_HOST=localhost
PHX_PORT=4000
SECRET_KEY_BASE=your_secret_key_base_here

# Development Configuration
MIX_ENV=dev
EOF
fi

# Create useful aliases and functions
print_status "Setting up development aliases..."
cat >> ~/.bashrc << 'EOF'

# Quotex Development Aliases
alias phx='mix phx.server'
alias iex='iex -S mix'
alias test='mix test'
alias testw='mix test.watch'
alias format='mix format'
alias credo='mix credo'
alias dialyzer='mix dialyzer'
alias deps='mix deps.get'
alias compile='mix compile'
alias clean='mix clean'

# Database aliases
alias db.setup='mix ecto.setup'
alias db.migrate='mix ecto.migrate'
alias db.rollback='mix ecto.rollback'
alias db.reset='mix ecto.reset'
alias db.seed='mix ecto.seed'

# Useful functions
phx_routes() {
    mix phx.routes | grep -i "${1:-.*}"
}

mix_help() {
    mix help | grep -i "${1:-.*}"
}
EOF

# Set up git configuration if not already configured
if [ -z "$(git config --global user.name)" ]; then
    print_status "Setting up Git configuration..."
    echo "Please configure Git with your information:"
    echo "  git config --global user.name 'Your Name'"
    echo "  git config --global user.email '<EMAIL>'"
fi

# Create development directories
print_status "Creating development directories..."
mkdir -p tmp/pids
mkdir -p priv/static
mkdir -p test/support

# Set proper permissions
chmod +x .devcontainer/setup.sh

print_success "Development environment setup complete!"
echo ""
print_status "🎉 Welcome to Quotex development!"
echo ""
print_status "Quick start commands:"
echo "  • Start Phoenix server: mix phx.server (or 'phx' alias)"
echo "  • Interactive Elixir: iex -S mix (or 'iex' alias)"
echo "  • Run tests: mix test (or 'test' alias)"
echo "  • Format code: mix format (or 'format' alias)"
echo "  • Database setup: mix ecto.setup (or 'db.setup' alias)"
echo ""
print_status "Services available:"
echo "  • Phoenix app: http://localhost:4000"
echo "  • LiveDashboard: http://localhost:4000/dashboard"
echo "  • PostgreSQL: localhost:5432"
echo "  • Redis: localhost:6379"
echo "  • pgAdmin (optional): http://localhost:8080"
echo "  • Redis Commander (optional): http://localhost:8081"
echo ""
print_status "To start optional GUI tools, run:"
echo "  docker-compose --profile tools up -d"
echo ""
