# Quotex Development Container

This directory contains the VS Code development container configuration for the Quotex project, providing a consistent and reproducible development environment for Elixir/Phoenix development.

## 🚀 Quick Start

1. **Prerequisites**: Ensure you have VS Code with the "Dev Containers" extension installed
2. **Open in Container**: Open this project in VS Code and select "Reopen in Container" when prompted
3. **Wait for Setup**: The container will build and run the setup script automatically
4. **Start Developing**: Once setup is complete, you're ready to develop!

## 🏗️ Architecture

The development environment includes:

### Core Services
- **App Container**: Elixir 1.15 with OTP 26, Phoenix, and development tools
- **PostgreSQL**: Database server (port 5432)
- **Redis**: Cache and session store (port 6379)

### Optional GUI Tools
- **pgAdmin**: PostgreSQL administration (port 8080)
- **Redis Commander**: Redis administration (port 8081)

## 🔧 Configuration Files

- `devcontainer.json`: Main VS Code devcontainer configuration
- `Dockerfile`: Custom Elixir/Phoenix development image
- `docker-compose.yml`: Multi-service orchestration
- `setup.sh`: Post-creation setup script
- `init-db.sql`: Database initialization

## 📦 Included Tools & Extensions

### Elixir/Phoenix Tools
- Elixir Language Server
- Phoenix Framework support
- Elixir Formatter
- Hex and Rebar package managers
- Phoenix generator

### Development Tools
- Git and GitHub CLI
- Node.js (compatible version) and npm/yarn for assets
- PostgreSQL and Redis clients
- Various VS Code extensions for productivity

### VS Code Extensions
- ElixirLS (Language Server)
- Phoenix Framework
- GitLens
- Docker support
- And many more for enhanced development experience

## 🌐 Port Forwarding

The following ports are automatically forwarded:

- **4000**: Phoenix development server
- **4001**: Phoenix LiveDashboard
- **5432**: PostgreSQL database
- **6379**: Redis server
- **8080**: pgAdmin (when using tools profile)
- **8081**: Redis Commander (when using tools profile)

## 📁 Volume Mounts

Performance-optimized volume mounts:
- `quotex-build-cache`: Preserves `_build` directory
- `quotex-deps-cache`: Preserves `deps` directory  
- `quotex-node-modules`: Preserves `assets/node_modules`

## 🔨 Development Workflow

### Starting a New Phoenix Project
If this is a fresh project, create a new Phoenix app:
```bash
mix phx.new . --app quotex --database postgres
```

### Common Commands
The setup script creates helpful aliases:
```bash
phx          # mix phx.server
iex          # iex -S mix
test         # mix test
format       # mix format
db.setup     # mix ecto.setup
db.migrate   # mix ecto.migrate
```

### Running Tests
```bash
mix test                    # Run all tests
mix test --watch           # Watch mode
mix test test/specific_test.exs  # Run specific test
```

### Database Operations
```bash
mix ecto.setup     # Create, migrate, and seed database
mix ecto.migrate   # Run migrations
mix ecto.rollback  # Rollback last migration
mix ecto.reset     # Drop, create, migrate, and seed
```

## 🛠️ Optional GUI Tools

To start the optional GUI tools (pgAdmin and Redis Commander):
```bash
docker-compose --profile tools up -d
```

Access them at:
- pgAdmin: http://localhost:8080 (<EMAIL> / admin)
- Redis Commander: http://localhost:8081

## 🔍 Troubleshooting

### Container Won't Start
1. Ensure Docker is running
2. Check for port conflicts
3. Try rebuilding: "Dev Containers: Rebuild Container"

### Database Connection Issues
1. Verify PostgreSQL is running: `pg_isready -h db -p 5432 -U postgres`
2. Check environment variables in `devcontainer.json`
3. Restart the database service: `docker-compose restart db`

### Redis Connection Issues
1. Test Redis connection: `redis-cli -h redis ping`
2. Check Redis logs: `docker-compose logs redis`

### Mix Dependencies Issues
1. Clean and reinstall: `mix deps.clean --all && mix deps.get`
2. Clear build cache: `mix clean`

## 📝 Environment Variables

Key environment variables configured:
- `MIX_ENV=dev`
- `DATABASE_URL=************************************/quotex_dev`
- `REDIS_URL=redis://redis:6379/0`
- `PHX_HOST=localhost`
- `PHX_PORT=4000`

## 🔄 Updating the Container

To update the development environment:
1. Modify the configuration files as needed
2. Rebuild the container: "Dev Containers: Rebuild Container"
3. The setup script will run automatically

## 📚 Additional Resources

- [Phoenix Framework Documentation](https://hexdocs.pm/phoenix/)
- [Elixir Documentation](https://hexdocs.pm/elixir/)
- [VS Code Dev Containers](https://code.visualstudio.com/docs/remote/containers)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
