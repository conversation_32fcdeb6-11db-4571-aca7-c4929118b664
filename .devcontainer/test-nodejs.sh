#!/bin/bash

# Test script to verify Node.js setup in the devcontainer
# This script can be run inside the container to verify Node.js is working correctly

echo "🧪 Testing Node.js setup in devcontainer..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test Node.js version
echo "Testing Node.js version..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js is installed: $NODE_VERSION"
else
    print_error "Node.js is not installed"
    exit 1
fi

# Test npm version
echo "Testing npm version..."
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_success "npm is installed: $NPM_VERSION"
else
    print_error "npm is not installed"
    exit 1
fi

# Test yarn version
echo "Testing Yarn version..."
if command -v yarn &> /dev/null; then
    YARN_VERSION=$(yarn --version)
    print_success "Yarn is installed: $YARN_VERSION"
else
    print_error "Yarn is not installed"
    exit 1
fi

# Test npm compatibility
echo "Testing npm compatibility..."
if npm --version &> /dev/null; then
    print_success "npm is working correctly"
else
    print_error "npm has compatibility issues"
    exit 1
fi

# Test basic npm functionality
echo "Testing npm functionality..."
if npm list -g --depth=0 &> /dev/null; then
    print_success "npm can list global packages"
else
    print_error "npm cannot list global packages"
    exit 1
fi

print_success "All Node.js tests passed! 🎉"
echo ""
print_info "Node.js setup is ready for Phoenix asset compilation"
echo ""
