version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
      - quotex-build-cache:/workspace/_build
      - quotex-deps-cache:/workspace/deps
      - quotex-node-modules:/workspace/assets/node_modules
    working_dir: /workspaces/${localWorkspaceFolderBasename}
    command: sleep infinity
    environment:
      - MIX_ENV=dev
      - DATABASE_URL=************************************/quotex_dev
      - REDIS_URL=redis://redis:6379/0
      - PHX_HOST=localhost
      - PHX_PORT=4000
    ports:
      - "4000:4000"  # Phoenix server
      - "4001:4001"  # LiveDashboard
    depends_on:
      - db
      - redis
    networks:
      - quotex-network

  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: quotex_dev
    volumes:
      - quotex-postgres-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - quotex-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - quotex-redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - quotex-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: Redis Commander for Redis GUI
  redis-commander:
    image: rediscommander/redis-commander:latest
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - quotex-network
    profiles:
      - tools

  # Optional: pgAdmin for PostgreSQL GUI
  pgadmin:
    image: dpage/pgadmin4:latest
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - quotex-pgadmin-data:/var/lib/pgadmin
    ports:
      - "8080:80"
    depends_on:
      - db
    networks:
      - quotex-network
    profiles:
      - tools

volumes:
  quotex-postgres-data:
  quotex-redis-data:
  quotex-pgadmin-data:
  quotex-build-cache:
  quotex-deps-cache:
  quotex-node-modules:

networks:
  quotex-network:
    driver: bridge
