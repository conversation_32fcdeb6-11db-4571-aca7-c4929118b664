{"name": "Quotex - Elixir/Phoenix Development", "dockerComposeFile": "docker-compose.yml", "service": "app", "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}", "customizations": {"vscode": {"extensions": ["jakebecker.elixir-ls", "phoenixframework.phoenix", "msaraiva.elixir-formatter", "ms-vscode.vscode-json", "bradlc.vscode-tailwindcss", "formulahendry.auto-rename-tag", "eamodio.gitlens", "github.vscode-pull-request-github", "ms-azuretools.vscode-docker", "ms-vscode-remote.remote-containers", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next", "redhat.vscode-yaml", "ms-vscode.hexeditor", "hbenl.vscode-test-explorer", "ms-vscode.test-adapter-converter", "streetsidesoftware.code-spell-checker", "gruntfuggly.todo-tree", "aaron-bond.better-comments", "oderwat.indent-rainbow", "christian-kohler.path-intellisense"], "settings": {"elixirLS.dialyzerEnabled": true, "elixirLS.fetchDeps": false, "elixirLS.suggestSpecs": true, "elixirLS.signatureAfterComplete": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": true, "source.organizeImports": true}, "editor.rulers": [80, 120], "editor.tabSize": 2, "editor.insertSpaces": true, "files.associations": {"*.ex": "elixir", "*.exs": "elixir", "*.eex": "phoenix-heex", "*.heex": "phoenix-heex", "*.leex": "phoenix-heex"}, "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.profiles.linux": {"bash": {"path": "/bin/bash"}}, "search.exclude": {"**/node_modules": true, "**/deps": true, "**/_build": true, "**/cover": true, "**/doc": true, "**/.elixir_ls": true}, "files.watcherExclude": {"**/_build/**": true, "**/deps/**": true, "**/node_modules/**": true, "**/.elixir_ls/**": true}}}}, "forwardPorts": [4000, 4001, 5432, 6379], "portsAttributes": {"4000": {"label": "Phoenix Server", "onAutoForward": "notify"}, "4001": {"label": "LiveDashboard", "onAutoForward": "silent"}, "5432": {"label": "PostgreSQL", "onAutoForward": "silent"}, "6379": {"label": "Redis", "onAutoForward": "silent"}}, "containerEnv": {"MIX_ENV": "dev", "DATABASE_URL": "************************************/quotex_dev", "REDIS_URL": "redis://redis:6379/0", "PHX_HOST": "localhost", "PHX_PORT": "4000"}, "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "postCreateCommand": ".devcontainer/setup.sh", "mounts": ["source=quotex-build-cache,target=${containerWorkspaceFolder}/_build,type=volume", "source=quotex-deps-cache,target=${containerWorkspaceFolder}/deps,type=volume", "source=quotex-node-modules,target=${containerWorkspaceFolder}/assets/node_modules,type=volume"], "remoteUser": "vscode", "shutdownAction": "stopCompose"}