# Use the official Elixir image as base
FROM elixir:1.15-otp-26-alpine

# Install system dependencies
RUN apk add --no-cache \
    build-base \
    git \
    curl \
    wget \
    bash \
    zsh \
    fish \
    openssh-client \
    postgresql-client \
    redis \
    nodejs \
    npm \
    yarn \
    python3 \
    py3-pip \
    inotify-tools \
    file \
    less \
    vim \
    nano \
    htop \
    tree \
    jq \
    ca-certificates \
    tzdata

# Set timezone
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create a non-root user
ARG USERNAME=vscode
ARG USER_UID=1000
ARG USER_GID=$USER_UID

RUN addgroup -g $USER_GID $USERNAME \
    && adduser -u $USER_UID -G $USERNAME -s /bin/bash -D $USERNAME \
    && mkdir -p /home/<USER>/.vscode-server /home/<USER>/.vscode-server-insiders \
    && chown -R $USERNAME:$USERNAME /home/<USER>

# Install Hex and <PERSON>bar
RUN mix local.hex --force && \
    mix local.rebar --force

# Install Phoenix
RUN mix archive.install hex phx_new --force

# Install Node.js LTS (for Phoenix assets)
RUN npm install -g npm@latest

# Set up workspace directory
WORKDIR /workspace
RUN chown -R $USERNAME:$USERNAME /workspace

# Switch to non-root user
USER $USERNAME

# Set up shell environment
RUN echo 'export MIX_ENV=dev' >> ~/.bashrc && \
    echo 'export PATH="$PATH:/workspace/_build/dev/lib/*/priv"' >> ~/.bashrc && \
    echo 'alias ll="ls -la"' >> ~/.bashrc && \
    echo 'alias la="ls -la"' >> ~/.bashrc && \
    echo 'alias iex="iex -S mix"' >> ~/.bashrc && \
    echo 'alias phx="mix phx.server"' >> ~/.bashrc && \
    echo 'alias test="mix test"' >> ~/.bashrc

# Install common Elixir development tools
RUN mix archive.install hex livebook --force

# Set default environment variables
ENV MIX_ENV=dev
ENV PHX_HOST=localhost
ENV PHX_PORT=4000

# Expose Phoenix default port
EXPOSE 4000

# Keep container running
CMD ["sleep", "infinity"]
