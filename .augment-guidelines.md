# Augment Guidelines

## Table of Contents

- [Language & Explanation Style](#language--explanation-style)
- [Thought Process and Debugging](#thought-process-and-debugging)
- [Interaction Preferences](#interaction-preferences)
- [Libraries & Frameworks](#libraries--frameworks)
- [Design Patterns](#design-patterns)
- [Anti-Patterns to Avoid](#anti-patterns-to-avoid)
- [Project Structure & Naming](#project-structure--naming)
- [Testing Preferences](#testing-preferences)
- [Observability & DevOps](#observability--devops)
- [Deployment & Scaling](#deployment--scaling)

---

## 💬 Language & Explanation Style

- Always use English for responses unless otherwise specified
- Explain Elixir or TypeScript code clearly, including:
  - Purpose of the function/module
  - How it interacts with Phoenix/Nebulex/Redis
  - Lifecycle implications (cache invalidation, process restart)
- If the response contains complex macros or DSLs, include a breakdown

---

## 🧠 Thought Process and Debugging

For every error or exception, suggest:

- Most likely cause
- Diagnostic steps (e.g., `IO.inspect`, `Logger.debug`)
- Fixes based on the context (Phoenix, Redis, <PERSON>ban, etc.)

---

## 🔄 Interaction Preferences

- Summarize long outputs with a "TL;DR" block if >30 lines
- Ask clarifying questions first if the request is ambiguous
- Provide working code examples unless the request is theoretical

---

## 📚 Libraries & Frameworks

### ✅ Use

- **Nebulex** for caching
- **Redis** as the primary cache backend
- **Oban** for background job processing and scheduling
- **Redix** only for direct Redis commands (e.g., pub/sub or pipelines)

### ❌ Avoid

- **Cachex**, unless Nebulex is unsuitable
- **Quantum** for scheduling — use `Oban.Plugins.Cron`

---

## 🔁 Design Patterns

### Caching

- Use `Nebulex.get_or_set/4` for lazy cache initialization
- Use versioned cache keys: `"entity:#{id}:v1"`

### Job Queues

- Oban.Worker modules must be named with the suffix `Worker`
- Jobs must be idempotent and retriable
- Prefer supervised processes for persistent cache clients

---

## 🚫 Anti-Patterns to Avoid

- ❌ Direct Redis commands unless absolutely necessary
- ❌ Global ETS tables for shared caching (prefer Nebulex Local or Redis)
- ❌ Using `Task.async/await` for periodic tasks — use Oban scheduling

---

## 🧱 Project Structure & Naming

- Cache modules go under `MyApp.Cache`
  - Example: `MyApp.Cache.UserCache`
- Oban Workers go under `MyApp.Workers`
  - Example: `MyApp.Workers.RebuildIndexWorker`
- Scheduled jobs use `Oban.Plugins.Cron` with cron expressions in `config.exs`

---

## 🧪 Testing Preferences

- For Nebulex, mock Redis via in-memory local adapter in tests
- For Oban, use `Oban.Testing.inline()` or `Oban.Testing.assert_enqueued()`
- All tests must be written in ExUnit, not ESpec

---

## 📈 Observability & DevOps

### Monitoring

Use PromEx or Telemetry to monitor:

- Cache hit/miss ratios
- Oban job performance (failures, durations)

### LiveDashboard

Use LiveDashboard with:

- Oban integration (`:oban_dashboard`)
- Nebulex stats via telemetry hooks

---

## 🔄 Deployment & Scaling

- Redis must be externally hosted or containerized (not local-only)
- All scheduled tasks must be idempotent and safe to run on multiple nodes
- Prefer stateless Phoenix nodes for cluster compatibility
